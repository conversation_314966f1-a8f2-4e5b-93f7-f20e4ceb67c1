{"@@locale": "en", "autoReconnect_cancel": "Cancel", "autoReconnect_message": "Reconnecting to device...", "bottomNav_call": "Calls", "bottomNav_chat": "Chats", "bottomNav_contacts": "Contacts", "bottomNav_profile": "Me", "callList_searchHint": "Search", "callList_title": "Call History", "callList_noMembersError": "Insufficient group members to start a call", "callList_loadMembersError": "Failed to load group members", "callList_selectCallType": "Select Call Type", "callList_selectCallTypeDesc": "This group has 2 members, please select call type:", "callList_pttTalk": "PTT Talk", "callList_voiceCall": "Voice Call", "callList_voiceCallNotImplemented": "Voice call feature is under development...", "callList_noRecords": "No call records", "callList_connected": "Connected", "callList_missed": "Missed", "callList_outgoing": "Outgoing", "callList_yesterday": "Yesterday", "voiceCall_calling": "Calling...", "voiceCall_ended": "Call ended", "voiceCall_demoMessage": "Voice call feature is under development. This is a demo interface.", "channelPicker_cancel": "Cancel", "channelPicker_channel": "Channel - {channel}", "channelPicker_title": "Select Channel", "chatList_connectDevice": "Connect Device", "chatList_connectionFailed": "Device connection failed: {error}", "chatList_connectionSuccess": "Device connected: {device}", "chatList_createGroup": "Create Group", "chatList_disconnectDevice": "Disconnect", "chatList_disconnectFailed": "Disconnect failed: {error}", "chatList_disconnectSuccess": "<PERSON><PERSON> disconnected", "chatList_joinGroup": "Join Group", "chatList_searchHint": "Search", "chatList_unpairFailed": "Unpair failed: {error}", "chatList_unpairSuccess": "Device unpaired", "createGroup_cancel": "Cancel", "createGroup_done": "Done", "createGroup_selectChannel": "Select Channel", "createGroup_setGroupName": "Group Name", "createGroup_setPassword": "Group Password", "createGroup_title": "Create Group", "deviceInfo_activateTurMass": "Activate TurMass™", "deviceInfo_bleVersion": "Bluetooth Firmware Version", "deviceInfo_deviceBattery": "Battery", "deviceInfo_deviceId": "Device ID", "deviceInfo_deviceId_failed": "Failed to get", "deviceInfo_deviceId_loading": "Loading…", "deviceInfo_deviceInfo": "Device Info", "deviceInfo_deviceModel": "Device Model", "deviceInfo_deviceName": "Device Name", "deviceInfo_deviceOperation": "Device Operation", "deviceInfo_deviceVersion": "Device Version", "deviceInfo_disconnect": "Disconnect Device", "deviceInfo_hwVersion": "Hardware Version", "deviceInfo_title": "Device Info", "deviceInfo_turMassFirmwareVersion": "TurMass™ Firmware Version", "deviceInfo_unpair": "Unpair Device", "global_appTitle": "aiTalk", "global_cancel": "Cancel", "joinGroup_title": "Join Group", "notification_showContent": "Show message content", "notification_showContent_desc": "Display message preview in notifications", "notification_system": "System notifications", "notification_system_desc": "Receive system push notifications", "notification_voiceCall": "Voice call notifications", "notification_voiceCall_desc": "Notify when there is an incoming voice call", "profile_avatar": "Avatar", "profile_developerMode": "Developer Mode", "profile_deviceId": "Device ID: {id}", "profile_deviceManagement": "Device Management", "profile_language": "Language", "profile_languageChinese": "Chinese", "profile_languageEnglish": "English", "profile_languageSystem": "System Default", "profile_myInfo": "My Info", "profile_nickname": "Nickname", "profile_nickname_default": "Default User", "profile_qrcode": "QR Code", "profile_settings": "Settings", "profile_signature": "Signature", "profile_userGuide": "User Guide", "publicChat_inputHint": "Type a message…", "publicChat_pressHold": "Press and hold to speak", "@publicChat_pressHold": {"description": "Public chat interface, voice mode prompt"}, "publicChat_recording": "Recording...", "@publicChat_recording": {"description": "Public chat interface, recording state prompt"}, "publicGroup_chatHistory": "Chat History", "publicGroup_chatInfo": "Chat Info", "publicGroup_deleteHistory": "Delete History", "publicGroup_detail_title": "Group Details", "publicGroup_editChannel": "Edit Channel", "publicGroup_groupName": "Group Name", "publicGroup_name": "Public Group - {channel}", "publicGroup_searchHistory": "Search History", "privateGroup_cancel": "Cancel", "privateGroup_changePassword": "Change Password", "privateGroup_chatHistory": "Chat History", "privateGroup_chatInfo": "Chat Info", "privateGroup_deleteHistory": "Delete History", "privateGroup_detail_title": "Group Details", "privateGroup_done": "Done", "privateGroup_editChannel": "Edit Channel", "privateGroup_groupChannel": "Group Channel", "privateGroup_groupMembers": "Group Members", "privateGroup_groupName": "Group Name", "privateGroup_groupPassword": "Group Password", "privateGroup_groupQRCode": "Group QR Code", "privateGroup_leaveGroup": "Disband Group", "privateGroup_searchHistory": "Search History", "settings_about": "About aiTalk", "settings_chatSettings": "<PERSON><PERSON>", "settings_displaySettings": "Display Settings", "settings_fontSize": "Font Size", "settings_helpAndFeedback": "Help & Feedback", "settings_multilanguage": "Language", "settings_notifications": "Notifications", "settings_other": "Others", "settings_storageManagement": "Storage Management", "settings_storageSettings": "Storage Settings", "settings_themeDark": "Dark", "settings_themeLight": "Light", "settings_themeMode": "Theme Mode", "settings_themeSystem": "System Default", "settings_title": "Settings", "settings_version": "Version {version}", "settings_voicePlayback": "Voice Playback", "voicePlayback_autoPlay": "Auto play voice", "voicePlayback_autoPlay_desc": "Automatically play received voice messages", "voicePlayback_backgroundMode": "Background playback mode", "voicePlayback_backgroundMode_desc": "Continue playing voice messages when app is in background", "chatAction_ptt": "PTT Talk", "@chatAction_ptt": {"description": "Label for PTT talk action in chat plus menu"}, "chatAction_voiceCall": "Voice Call", "@chatAction_voiceCall": {"description": "Label for voice call action in chat plus menu"}, "voiceCall_waitingAnswer": "Waiting for answer...", "@voiceCall_waitingAnswer": {"description": "Waiting for answer text in voice call dialing screen"}, "voiceCall_hangUp": "Hang Up", "@voiceCall_hangUp": {"description": "Hang up button in voice call screen"}, "voiceCall_speaker": "Speaker", "@voiceCall_speaker": {"description": "Speaker button in voice call screen"}, "voiceCall_earpiece": "Earpiece", "@voiceCall_earpiece": {"description": "Earpiece mode in voice call screen"}, "voiceCall_incomingCall": "Incoming Call", "@voiceCall_incomingCall": {"description": "Title for incoming call screen"}, "voiceCall_realTimeCall": "Real-time Call", "@voiceCall_realTimeCall": {"description": "Call type indicator in incoming call screen"}, "voiceCall_accept": "Accept", "@voiceCall_accept": {"description": "Accept button in incoming call screen"}, "voiceCall_reject": "Reject", "@voiceCall_reject": {"description": "Reject button in incoming call screen"}, "ptt_noMembers": "No members", "@ptt_noMembers": {"description": "Placeholder shown when there are no group members in PTT screen"}, "storage_totalTitle": "aiTalk Data", "storage_cache": "<PERSON><PERSON>", "storage_cacheDesc": "Cache is used to temporarily store data generated during the app's operation, such as images, file previews, and temporary messages. Clearing the cache can free up storage space without affecting your chat history or personal settings.", "storage_cacheClear": "Clear", "storage_cacheCleared": "<PERSON><PERSON> cleared", "storage_chathistory": "Chat History", "storage_chathistoryDesc": "Chat History contains all your conversations with other users, including text, images, and files. Deleting chat history will permanently delete all conversation content and cannot be undone. Please proceed with caution.", "storage_chathistoryManage": "Manage", "storage_appdata": "App Data", "storage_appdataDesc": "App Data refers to the storage space occupied by the application itself, including essential runtime files and model parameters for voice algorithms. These data are required for the app to function properly and cannot be deleted.", "settings_textSize": "Text Size", "textSize_demoMessage": "I'm {name}", "chatList_voicePreview": "[Voice]", "chatList_locationPreview": "[Location]", "chatList_messagePreview": "[Message]"}